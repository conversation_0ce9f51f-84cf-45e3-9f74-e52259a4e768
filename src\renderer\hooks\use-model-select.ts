import { useEnabledSortedModels } from "@renderer/queries/hooks/use-models";
import { useProviders } from "@renderer/queries/hooks/use-providers";
import type { Model, Provider } from "@shared/triplit/types";
import { useCallback, useState } from "react";

interface UseModelSelectOptions {
  onSelect?: (modelId: string) => void | Promise<void>;
}

interface UseModelSelectReturn {
  providers: Provider[];
  models: Model[];
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  handleToggleOpen: () => void;
  handleModelSelect: (modelId: string) => void;
  isLoading: boolean;
}

export function useModelSelect(
  options: UseModelSelectOptions = {},
): UseModelSelectReturn {
  const { onSelect } = options;

  const { data: providers = [], isFetching: providersFetching } = useProviders({
    enabled: true,
  });

  const { data: models = [], isFetching: modelsFetching } =
    useEnabledSortedModels({
      enabled: true,
    });

  // Modal state management
  const [isOpen, setIsOpen] = useState(false);

  const handleToggleOpen = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  const handleModelSelect = useCallback(
    async (modelId: string) => {
      setIsOpen(false);
      if (onSelect) {
        await onSelect(modelId);
      }
    },
    [onSelect],
  );

  const isLoading = providersFetching || modelsFetching;

  return {
    providers,
    models,
    isOpen,
    setIsOpen,
    handleToggleOpen,
    handleModelSelect,
    isLoading,
  };
}
