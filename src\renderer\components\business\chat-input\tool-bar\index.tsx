import plane from "@renderer/assets/images/plane.svg?url";
import { But<PERSON> } from "@renderer/components/ui/button";
import { Separator } from "@renderer/components/ui/separator";
import type { AttachmentFile } from "@renderer/hooks/use-attachments";
import { useGlobalShortcutHandler } from "@renderer/hooks/use-global-shortcut-handler";
import { cn } from "@renderer/lib/utils";
import { useEnabledModelWithProvider } from "@renderer/queries/hooks/use-models";

import { useCallback } from "react";
import { ActionGroup } from "./action-group";
import { ModelSelect } from "./model-select";

interface ToolBarProps {
  className?: string;
  onFilesSelect: (files: FileList) => void;
  attachments: AttachmentFile[];
  onSendMessage: () => Promise<void>;
  selectedModelId: string;
  onModelSelect: (modelId: string) => Promise<void>;
  isDisabled: boolean;
  setEditMessageId: (messageId: string | null) => void;
}

export function ToolBar({
  className,
  onFilesSelect,
  onSendMessage,
  selectedModelId,
  onModelSelect,
  isDisabled,
  setEditMessageId,
}: ToolBarProps) {
  const { data: model } = useEnabledModelWithProvider(selectedModelId);
  const disabled = model?.provider?.apiType !== "302ai";

  const handleSendMessageClick = useCallback(async () => {
    if (isDisabled) return;
    setEditMessageId(null);
    await onSendMessage();
  }, [isDisabled, setEditMessageId, onSendMessage]);

  const handleSendMessageShortcut = useCallback(() => {
    if (!isDisabled) {
      handleSendMessageClick();
    }
  }, [isDisabled, handleSendMessageClick]);

  //! TODO: 待迁移
  useGlobalShortcutHandler("send-message", handleSendMessageShortcut);

  return (
    <div
      className={cn(
        "flex h-[var(--chat-input-toolbar-height)] flex-row items-center justify-between",
        className,
      )}
    >
      <div className="flex h-full w-full flex-row justify-between">
        <ActionGroup onFilesSelect={onFilesSelect} disabled={disabled} />

        <div className="flex flex-row items-center gap-x-2">
          <ModelSelect
            onSelect={onModelSelect}
            selectedModelId={selectedModelId}
          />

          <Separator className="h-1/2 w-[2px]" orientation="vertical" />
          {/* <Button
            intent="primary"
            size="square-petite"
            shape="circle"
            onClick={handleSendMessageClick}
            isDisabled={isDisabled}
            // className="!rounded-lg"
          >
            <Send className="!size-4" />
          </Button> */}
          <Button
            size="sq-sm"
            isCircle={false}
            onClick={handleSendMessageClick}
            isDisabled={isDisabled}
            className="rounded-[10px] bg-primary text-fg"
          >
            <img src={plane} alt="plane" className="size-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}
