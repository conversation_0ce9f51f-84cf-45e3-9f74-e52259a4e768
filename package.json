{"displayName": "302AIStudio", "name": "302AIStudio", "productName": "302 AI Studio", "appId": "com.302ai.302aistudio", "description": "302 AI Studio", "version": "25.31.1", "main": "./node_modules/.dev/main/index.js", "homepage": "https://302.ai", "resources": "src/resources", "author": {"name": "302.AI", "email": "<EMAIL>"}, "license": "AGPL-3.0", "scripts": {"start": "electron-vite preview", "dev": "electron-vite dev --watch --noSandbox", "build": "npm run prebuild && npm run typecheck", "build:original": "npm run typecheck && npm run prebuild && electron-builder", "build:check": "yarn typecheck", "build:unpack": "npm run build && electron-builder --dir", "compile:app": "electron-vite build", "compile:packageJSON": "tsx ./src/lib/electron-app/release/modules/prebuild.ts", "prebuild": "run-s clean:dev compile:app compile:packageJSON", "install:deps": "electron-builder install-app-deps", "make:release": "tsx ./src/lib/electron-app/release/modules/release.ts", "clean:dev": "rimraf ./node_modules/.dev", "lint": "biome lint --no-errors-on-unmatched", "lint:fix": "biome lint --write --no-errors-on-unmatched", "typecheck": "npm run typecheck:node && npm run typecheck:web", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "prepare": "husky", "commit": "git-cz", "commitlint": "commitlint --edit", "postinstall": "husky", "prepack": "pinst --disable", "postpack": "pinst --enable"}, "dependencies": {"@better-fetch/fetch": "^1.1.18", "@hello-pangea/dnd": "^18.0.1", "@intentui/icons": "^1.10.33", "@lobehub/icons-static-svg": "^1.53.0", "@monaco-editor/react": "^4.7.0", "@triplit/client": "^1.0.40", "@triplit/react": "^1.0.40", "@triplit/server": "^1.0.50", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "electron-router-dom": "^2.1.0", "electron-updater": "6.6.2", "file-type": "^21.0.0", "i18next": "^25.0.0", "inversify": "^7.5.2", "katex": "^0.16.11", "ldrs": "^1.1.7", "linkify-react": "^4.3.1", "linkifyjs": "^4.3.1", "lodash-es": "^4.17.21", "lucide-react": "^0.477.0", "mammoth": "^1.9.1", "mermaid": "^11.6.0", "mitt": "^3.0.1", "motion": "^12.9.2", "nanoid": "^5.1.5", "openai": "^5.10.1", "pdf-parse-new": "^1.3.9", "portfinder": "^1.0.37", "react": "^19.1.0", "react-aria-components": "^1.8.0", "react-dom": "^19.1.0", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.2.0", "react-window": "^1.8.11", "reflect-metadata": "^0.2.2", "rehype-katex": "^7.0.1", "remark-cjk-friendly": "^1.2.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sharp": "^0.34.2", "shiki": "^3.6.0", "sonner": "^2.0.3", "tailwindcss-react-aria-components": "^2.0.0", "tokenx": "^1.0.1", "turndown": "^7.2.0", "unist-util-visit": "^5.0.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2", "zod": "^3.25.49"}, "devDependencies": {"@babel/core": "^7.28.0", "@biomejs/biome": "^2.0.6", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/tsconfig": "^1.0.1", "@electron-toolkit/utils": "^4.0.0", "@electron/notarize": "patch:@electron/notarize@npm%3A3.0.1#~/.yarn/patches/@electron-notarize-npm-3.0.1-cc88e64e60.patch", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/vite": "^4.1.4", "@triplit/cli": "^1.0.50", "@types/babel__core": "^7", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.8", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-window": "^1.8.8", "@types/semver": "^7.5.8", "@types/xml2js": "^0", "@vitejs/plugin-react": "^4.3.4", "babel-plugin-react-compiler": "^19.1.0-rc.2", "code-inspector-plugin": "^0.20.1", "commitizen": "^4.3.0", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "electron": "^35.1.5", "electron-builder": "26.0.12", "electron-extension-installer": "^1.2.0", "electron-log": "^5.3.4", "electron-rebuild": "^3.2.9", "electron-store": "^8.2.0", "electron-vite": "^3.1.0", "electron-window-state": "^5.0.3", "eslint-plugin-react-hooks": "6.0.0-rc.1", "husky": "^9.1.7", "npm-run-all": "^4.1.5", "open": "^10.1.0", "rimraf": "^6.0.1", "rollup-plugin-inject-process-env": "^1.3.1", "semver": "^7.5.4", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.4", "tsx": "^4.19.3", "tw-animate-css": "^1.2.5", "typescript": "^5.1.6", "vite": "^6.3.3", "vite-plugin-babel": "^1.3.2", "vite-tsconfig-paths": "^5.1.4"}, "packageManager": "yarn@4.9.2", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependenciesMeta": {"sharp": {"built": false}}, "resolutions": {"@electron/notarize@npm:^3.0.1": "patch:@electron/notarize@npm%3A3.0.1#~/.yarn/patches/@electron-notarize-npm-3.0.1-cc88e64e60.patch"}}