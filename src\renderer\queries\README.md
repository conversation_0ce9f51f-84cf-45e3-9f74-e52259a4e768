# 查询系统开发指南

本项目的数据获取系统采用了三层架构设计，为开发者提供了类型安全、可复用且高性能的数据查询方案。

## 🏗️ 架构概览

### 三层架构设计

```
src/renderer/queries/
├── definitions/        # 查询定义层 - 定义可复用的查询构建器
├── hooks/             # 基础Hook层 - 将查询定义包装为React Hook
│   ├── composed/      # 组合Hook层 - 组合多个基础Hook处理复杂场景
│   └── use-*.ts       # 各集合的基础Hook
├── types.ts           # 类型定义
└── index.ts           # 统一导出
```

### 核心设计理念

1. **类型安全优先**: 基于数据库Schema自动生成类型，编译时验证
2. **关注点分离**: 查询逻辑、React集成、业务逻辑分层处理
3. **一致性接口**: 所有Hook都返回统一的`QueryResult`结构
4. **可配置性**: 支持条件查询、数据转换等灵活配置

## 📋 基础用法

### 1. 导入查询Hook

```typescript
import { 
  useMessages, 
  useThreads, 
  useChatContext 
} from '@renderer/queries';
```

### 2. 使用集合查询

```typescript
// 获取所有消息
const { data: messages, isLoading, error } = useMessages();

// 获取排序后的消息
const { data: orderedMessages } = useOrderedMessages();

// 根据线程ID获取消息
const { data: threadMessages } = useMessagesByThread(threadId);

// 单条记录查询
const { data: thread } = useThread(threadId);
```

### 3. 查询配置选项

```typescript
// 条件查询 - 只有在threadId存在时才执行查询
const { data: messages } = useMessagesByThread(threadId, {
  enabled: !!threadId
});

// 数据转换 - 只获取第一条消息
const { data: firstMessage } = useMessages({
  select: (messages) => messages[0] || null
});

// 组合配置
const { data: recentMessages } = useOrderedMessages({
  enabled: isActive,
  select: (messages) => messages.slice(-10) // 最近10条
});
```

## 🔧 查询系统详解

### 查询结果接口

所有查询Hook都返回统一的`QueryResult`接口：

```typescript
interface QueryResult<TData> {
  data: TData;           // 查询结果数据
  isLoading: boolean;    // 初始加载状态
  isFetching: boolean;   // 后台刷新状态（包括初始加载）
  error: Error | null;   // 错误信息
}
```

### 查询定义模式

每个集合都有对应的查询定义文件，提供标准化的查询方法：

```typescript
// 基础查询接口
interface BaseQueries<T> {
  all: () => QueryBuilder<T>;        // 获取所有记录
  byId: (id: string) => QueryBuilder<T>; // 根据ID查询
}

// 支持排序的查询
interface OrderableQueries<T> extends BaseQueries<T> {
  ordered: () => QueryBuilder<T>;    // 获取排序后的记录
}

// 支持过滤的查询
interface FilterableQueries<T> extends BaseQueries<T> {
  enabled: () => QueryBuilder<T>;    // 获取启用的记录
}
```

### 集合特定查询

每个集合都提供自己特有的查询方法：

```typescript
// 消息查询 (messages)
const { data } = useMessagesByThread(threadId);           // 按线程查询
const { data } = useOrderedMessagesByThread(threadId);    // 按线程查询并排序

// 提供商查询 (providers)  
const { data } = useEnabledProviders();                   // 获取启用的提供商
const { data } = useEnabledOrderedProviders();            // 获取启用且排序的提供商

// 模型查询 (models)
const { data } = useEnabledModels();                      // 获取启用的模型
const { data } = useEnabledSortedModels();                // 获取启用且排序的模型
```

## 🎯 组合Hook：处理复杂场景

### useChatContext - 聊天上下文

聚合所有聊天相关的基础数据，提供计算属性：

```typescript
const {
  // 原始数据
  settings,
  providers, 
  models,
  ui,
  
  // 计算属性
  selectedProvider,    // 当前选中的提供商
  selectedModel,       // 当前选中的模型
  newChatModel,        // 新聊天使用的模型
  
  // 便捷访问
  theme,              // 主题设置
  language,           // 语言设置
  isPrivate,          // 隐私模式
  activeThreadId,     // 活跃线程ID
  activeTabId,        // 活跃标签页ID
  
  // 状态
  isLoading,          // 是否正在加载
  isReady             // 数据是否就绪
} = useChatContext();

// 使用示例
if (!isReady) {
  return <LoadingSpinner />;
}

// 数据已就绪，可以安全使用
console.log('当前模型:', selectedModel?.name);
console.log('可用提供商:', providers.length);
```

### useActiveSession - 活跃会话管理

管理当前活跃的线程和标签页状态：

```typescript
const {
  // 基础数据
  activeThreadId,      // 活跃线程ID
  activeTabId,         // 活跃标签页ID  
  activeThread,        // 活跃线程对象
  activeTab,           // 活跃标签页对象
  messages,            // 当前线程的消息列表
  
  // 计算状态
  hasActiveThread,     // 是否有活跃线程
  hasActiveTab,        // 是否有活跃标签页
  hasMessages,         // 是否有消息
  isNewSession,        // 是否为新会话
  isSessionStarted,    // 会话是否已开始
  
  // 统计信息
  messageCount,        // 消息总数
  lastMessage,         // 最后一条消息
  userMessageCount,    // 用户消息数
  assistantMessageCount // 助手消息数
} = useActiveSession();

// 使用示例
if (isNewSession) {
  return <WelcomeScreen />;
}

if (!hasMessages) {
  return <EmptyConversation />;
}
```

### useModelSelection - 模型选择逻辑

处理复杂的模型选择和管理逻辑：

```typescript
const {
  // 模型数据
  models,              // 所有可用模型
  enabledModels,       // 启用的模型
  sortedModels,        // 排序后的模型
  modelsByProvider,    // 按提供商分组的模型
  
  // 选择状态
  selectedModel,       // 当前选中的模型
  availableModels,     // 当前提供商的可用模型
  
  // 操作方法
  selectModel,         // 选择模型的方法
  canSelectModel,      // 是否可以选择模型
  
  // 状态
  isLoading,
  hasModels
} = useModelSelection();
```

## 💡 最佳实践

### 1. 错误处理和加载状态

```typescript
const { data: messages, isLoading, error } = useMessages();

// 处理加载状态
if (isLoading) {
  return <Skeleton />;
}

// 处理错误状态
if (error) {
  return <ErrorAlert message={error.message} />;
}

// 处理空数据
if (!data || data.length === 0) {
  return <EmptyState />;
}

// 正常渲染数据
return <MessageList messages={data} />;
```

### 2. 条件查询

```typescript
// 避免无效查询
const { data: thread } = useThread(threadId, {
  enabled: !!threadId && threadId !== 'new'
});

// 根据用户权限查询
const { data: sensitiveData } = useSensitiveData({
  enabled: user?.hasPermission
});
```

### 3. 数据转换

```typescript
// 计算派生数据
const { data: messageStats } = useMessages({
  select: (messages) => ({
    total: messages.length,
    unread: messages.filter(m => !m.read).length,
    lastMessage: messages[messages.length - 1]
  })
});

// 格式化数据
const { data: formattedThreads } = useThreads({
  select: (threads) => threads.map(thread => ({
    ...thread,
    displayName: thread.title || '未命名对话',
    lastActivity: formatTime(thread.updatedAt)
  }))
});
```

### 4. 性能优化

```typescript
// 使用组合Hook减少重复查询
const chatContext = useChatContext();
// 比手动组合多个Hook更高效：
// const settings = useSettings();
// const providers = useProviders();
// const models = useModels();

// 避免在渲染函数中创建查询参数
const threadId = useMemo(() => computeThreadId(), [dependency]);
const { data } = useMessagesByThread(threadId);
```

### 5. 类型安全

```typescript
// 利用TypeScript类型推导
const { data: messages } = useMessages(); // messages自动推导为Message[]类型

// 自定义选择器保持类型安全
const { data: messageCount } = useMessages({
  select: (messages): number => messages.length // 明确返回类型
});
```

## 🚀 扩展指南

### 添加新的查询定义

1. 在`definitions/`下创建新的查询定义文件：

```typescript
// definitions/my-collection-queries.ts
import { triplitClient } from "@renderer/client";
import type { BaseQueries } from "../types";

export const myCollectionQueries: BaseQueries<"myCollection"> = {
  all: () => triplitClient.query("myCollection"),
  byId: (id: string) => triplitClient.query("myCollection").Id(id),
  // 添加自定义查询方法
  byStatus: (status: string) => 
    triplitClient.query("myCollection").Where("status", "=", status)
};
```

2. 创建对应的Hook文件：

```typescript
// hooks/use-my-collection.ts
import { myCollectionQueries } from "../definitions/my-collection-queries";
import { useStandardQuery, useStandardQueryOne } from "./use-standard-query";
import type { QueryConfig, QueryOneConfig, QueryResult } from "../types";

export function useMyCollection(config?: QueryConfig<"myCollection">) {
  return useStandardQuery(() => myCollectionQueries.all(), config);
}

export function useMyCollectionByStatus(
  status: string,
  config?: QueryConfig<"myCollection">
) {
  return useStandardQuery(() => myCollectionQueries.byStatus(status), config);
}
```

3. 在索引文件中导出：

```typescript
// hooks/index.ts
export * from "./use-my-collection";

// index.ts
export * from "./hooks";
```

### 创建组合Hook

```typescript
// hooks/composed/use-my-complex-data.ts
export function useMyComplexData() {
  const { data: dataA, isLoading: loadingA } = useDataA();
  const { data: dataB, isLoading: loadingB } = useDataB();
  
  const aggregatedData = useMemo(() => {
    if (!dataA || !dataB) return null;
    
    return {
      combined: [...dataA, ...dataB],
      summary: {
        totalA: dataA.length,
        totalB: dataB.length
      }
    };
  }, [dataA, dataB]);
  
  return {
    data: aggregatedData,
    isLoading: loadingA || loadingB,
    isReady: !!(dataA && dataB)
  };
}
```

## 🐛 常见问题

### Q: 查询结果为空但数据库中有数据？
A: 检查查询条件和`enabled`配置，确保查询条件正确且查询已启用。

### Q: 组件重新渲染过于频繁？
A: 使用`useMemo`包装查询参数，避免在每次渲染时创建新的对象。

### Q: 如何调试查询性能？
A: 使用React DevTools Profiler和Triplit的调试工具监控查询性能。

### Q: 数据更新后组件没有重新渲染？
A: 确保使用了Triplit的响应式查询，数据变更会自动触发组件更新。

## 📚 参考资料

- [Triplit 官方文档](https://triplit.dev)
- [React Query 模式](https://react-query.tanstack.com)
- [TypeScript 高级类型](https://www.typescriptlang.org/docs/handbook/2/types-from-types.html)

---

这个查询系统为项目提供了强大而灵活的数据获取能力，遵循最佳实践并保持代码的可维护性。在使用过程中如有问题，请参考本指南或查看现有代码示例。