import { triplitClient } from "@renderer/client";
import type { FullQueries } from "../types";

/**
 * MCP Servers 集合的标准查询定义
 */
export const mcpServersQueries: FullQueries<"mcpServers"> & {
  /** 获取启用且按顺序排列的 MCP servers */
  enabledOrdered: () => ReturnType<typeof triplitClient.query<"mcpServers">>;
  /** 根据类型获取 MCP servers */
  byType: (type: "stdio" | "sse" | "streamableHTTP") => ReturnType<typeof triplitClient.query<"mcpServers">>;
  /** 获取启用的特定类型 MCP servers */
  enabledByType: (type: "stdio" | "sse" | "streamableHTTP") => ReturnType<typeof triplitClient.query<"mcpServers">>;
} = {
  /**
   * 获取所有 MCP servers 记录
   */
  all: () => triplitClient.query("mcpServers"),

  /**
   * 获取启用的 MCP servers
   */
  enabled: () => triplitClient.query("mcpServers").Where("enabled", "=", true),

  /**
   * 获取按顺序排列的 MCP servers
   */
  ordered: () => triplitClient.query("mcpServers").Order("order", "ASC"),

  /**
   * 获取启用且按顺序排列的 MCP servers
   */
  enabledOrdered: () =>
    triplitClient
      .query("mcpServers")
      .Where("enabled", "=", true)
      .Order("order", "ASC"),

  /**
   * 根据类型获取 MCP servers
   */
  byType: (type: "stdio" | "sse" | "streamableHTTP") =>
    triplitClient.query("mcpServers").Where("type", "=", type),

  /**
   * 获取启用的特定类型 MCP servers
   */
  enabledByType: (type: "stdio" | "sse" | "streamableHTTP") =>
    triplitClient
      .query("mcpServers")
      .Where("enabled", "=", true)
      .Where("type", "=", type)
      .Order("order", "ASC"),

  /**
   * 根据ID获取单个 MCP server
   */
  byId: (id: string) => triplitClient.query("mcpServers").Id(id),
} as const;