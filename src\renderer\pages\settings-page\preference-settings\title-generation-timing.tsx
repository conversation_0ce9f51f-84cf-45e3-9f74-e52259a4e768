import { Label } from "@renderer/components/ui/field";
import { Select } from "@renderer/components/ui/select";
import { cn } from "@renderer/lib/utils";
import { useTranslation } from "react-i18next";

export function TitleGenerationTiming() {
  const { t } = useTranslation("translation", {
    keyPrefix: "settings.preference-settings.title-generation-timing",
  });

  const titleGenerationTimingOptions = [
    { key: "first-time", label: t("first-time") },
    { key: "every-time", label: t("every-time") },
    { key: "off", label: t("off") },
  ];

  return (
    <div className="flex flex-col gap-2">
      <Label className="text-label-fg">{t("label")}</Label>
      <Select
        className="min-w-full"
        selectedKey={titleGenerationTimingOptions[0].key}
        onSelectionChange={() => {}}
        aria-label="Title Generation Timing"
      >
        <Select.Trigger className="inset-ring-transparent h-11 rounded-[10px] bg-setting text-setting-fg transition-none hover:inset-ring-transparent" />
        <Select.List
          className="min-w-full"
          items={titleGenerationTimingOptions}
        >
          {({ key, label }) => (
            <Select.Option
              className={cn(
                "flex cursor-pointer justify-between",
                "[&>[data-slot='check-indicator']]:order-last [&>[data-slot='check-indicator']]:mr-0 [&>[data-slot='check-indicator']]:ml-auto",
              )}
              key={key}
              id={key}
              textValue={label}
            >
              <span className="flex items-center gap-2">
                <span className="text-sm">{label}</span>
              </span>
            </Select.Option>
          )}
        </Select.List>
      </Select>
    </div>
  );
}
