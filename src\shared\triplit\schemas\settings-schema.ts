import { Schema as S } from "@triplit/client";

export const settingsSchema = {
  schema: S.<PERSON>a({
    id: S.Id({ format: "nanoid" }),
    enableWebSearch: <PERSON><PERSON>({ default: false }),
    enableReason: <PERSON><PERSON>({ default: false }),
    searchService: S.String({
      enum: ["search1api", "tavily", "exa", "bochaai"],
      default: "search1api",
    }),
    theme: S.String({
      enum: ["light", "dark", "system"],
      default: "system",
    }),
    language: S.String({
      enum: ["zh", "en", "ja"],
      default: "zh",
    }),
    selectedModelId: S.String({ default: "" }),
    autoUpdate: <PERSON><PERSON>({ default: true }),
    displayAppStore: S.<PERSON>({ default: true }),
    defaultPrivacyMode: S.<PERSON>({ default: false }),
    isPrivate: <PERSON><PERSON>({ default: false }),
    feedUrl: S.String({
      default:
        "https://github.com/302ai/302-AI-Studio/releases/latest/download",
    }),
    // Streaming output configuration
    streamSmootherEnabled: <PERSON>.<PERSON>(<PERSON><PERSON>({ default: false })),
    streamSpeed: S.Optional(
      S.String({
        enum: ["slow", "normal", "fast"],
        default: "normal",
      }),
    ),
    collapseCodeBlock: S.Optional(S.Boolean({ default: false })),
    hideReason: S.Optional(S.Boolean({ default: false })),
    collapseThinkBlock: S.Optional(S.Boolean({ default: false })),
    disableMarkdown: S.Optional(S.Boolean({ default: false })),
    newChatModelId: S.Optional(S.String({ default: "use-last-model" })),
    titleModelId: S.Optional(S.String({ default: "use-current-chat-model" })),
    enableUrlParse: S.Optional(S.Boolean({ default: true })),
  }),
};
