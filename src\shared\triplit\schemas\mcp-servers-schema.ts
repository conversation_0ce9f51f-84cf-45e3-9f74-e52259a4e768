import { Schema as S } from "@triplit/client";

export const mcpServersSchema = {
  schema: S.Schema({
    id: S.Id({ format: "nanoid" }),
    name: S.String(),
    description: S.String(),
    type: S.String({
      enum: ["stdio", "sse", "streamableHTTP"],
    }),
    url: S.String({ nullable: true }),
    command: S.String({ nullable: true }),
    icon: S.String(),
    enabled: S.Boolean({ default: true }),
    order: S.Number({ default: 0 }),
    createdAt: S.Date({ default: S.Default.now() }),
    updatedAt: S.Date({ default: S.Default.now() }),
    advancedSettings: S.Optional(
      S.Record({
        timeout: S.Optional(S.Number()),
        customHeaders: S.Optional(S.Record({})),
        customEnvVars: S.Optional(S.Record({})),
        autoUseTool: S.Optional(S<PERSON>()),
        keepLongTaskConnection: S.Optional(<PERSON><PERSON>()),
      }),
    ),
  }),
  relationships: {
    tools: S.RelationMany("mcpTools", {
      where: [["mcpServerId", "=", "$id"]],
    }),
  },
};