{"chat": {"input-label": "Enter a message", "file-not-found": "File not found", "file-preview-failed": "Failed to preview file", "collected": "Favorites", "input-placeholder": "Enter your message here. Shift+Enter to line break", "send-failed": "Failed to send message", "tool-bar": {"attach-file": "Attach file(file size cannot exceed 10MB)", "online-search": "Online Search", "thinking": "Thinking", "disabled": "Only models from 302.AI Provider are available"}, "model-select-label": "Select a model", "model-select-placeholder": "Please select a model...", "model-select": "Set Model", "model-search-placeholder": "Search...", "no-models-found": "No matching model was found...", "lack-model": "Please select a model first!", "lack-provider": "Missing model provider!", "edit-message": "Edit Message", "edit-message-only-save": "Only Save", "function_call": "Tool Call", "reasoning": "Reasoning", "vision": "Vision", "file": "File", "music": "Audio", "video": "Video", "support": "Support"}, "settings": {"about-settings": {"name": "About", "version": "Version", "description": {"title": "Application Description", "content": "302 AI Studio is a powerful AI chat application that provides users with intelligent conversation experiences. It supports multiple AI models, features file upload and web search capabilities, and is committed to creating the best AI interaction platform for users."}, "website": {"title": "Official Website"}, "copyright": {"title": "Copyright", "content": "© 2024 302.AI. All rights reserved."}, "license": {"title": "License", "content": "MIT License"}, "help-center": {"title": "Help Center"}, "terms-of-service": {"title": "Terms of Service"}, "privacy-policy": {"title": "Privacy Policy"}}, "assistant-settings": {"name": "Assistant Settings"}, "general-settings": {"language": {"label": "Language"}, "name": "General", "theme": {"dark": "Dark", "label": "Theme", "light": "Light", "system": "System"}, "privacy-mode": {"title": "Privacy Mode", "description": "Auto Inherit"}, "version-update": {"label": "Version Update", "check-for-updates": "Check", "switch": {"label": "Automatic Update"}, "version-info": "Version", "update-available": "New version detected", "update-now": "Update Now", "update-later": "Update Later", "checking": "Checking", "no-update-available": "The current version is already the latest version", "check-failed": "Check failed, please try again", "restart-to-update": "<PERSON><PERSON>", "downloading": "Downloading", "new-version-available": "New Version Available", "update-downloaded": "The new version is ready, please restart the app to install it", "new-version-downloaded": "New Version Ready"}}, "icon-tooltip": "Open Settings", "model-settings": {"model-list": {"label": "Model List", "no-models-description": "No models available...", "current": "Current", "collected": "Favorites", "search-placeholder": "Search...", "fetch-models": "Fetch Models", "add-model": "Add Model", "clear-models": "Clear", "model-name": "Model Name", "model-capabilities": "Capabilities", "model-type": "Model Type", "actions": "Actions"}, "add-model-modal": {"title": "Add Model", "edit-title": "Edit Model", "model-id": {"label": "Model ID", "placeholder": "Enter model ID", "description": "This is the real name of the model, used for actual requests", "required-error": "Model ID cannot be empty"}, "description": {"label": "Description", "placeholder": "Enter description", "description": "You can set a more recognizable name for this model, for display only"}, "capabilities": {"label": "Capabilities", "reasoning": "Reasoning", "vision": "Vision", "music": "Music", "video": "Video", "function_call": "Tool Call"}, "type": {"label": "Model Type", "language": "Language", "image-generation": "Image Generation", "tts": "TTS", "embedding": "Embedding", "rerank": "<PERSON><PERSON>"}, "actions": {"cancel": "Cancel", "save": "Save", "add-success": "Add Success", "edit-success": "Edit Success", "add-error-message": "Add Error", "edit-error-message": "<PERSON>r", "delete-error-message": "Delete Error", "delete-success-message": "Delete Success", "delete-title": "Delete Model", "delete-description": "Are you sure you want to delete this model?", "delete-confirm-text": "Delete", "clear-title": "Clear All Models", "clear-description": "Are you sure you want to clear all models from this provider? This action cannot be undone.", "clear-confirm-text": "Clear All", "clear-success-message": "All models cleared successfully", "clear-error-message": "Failed to clear models"}}, "model-provider": {"add-provider": "Add Model", "custom-provider": "Custom Provider", "add": "Add", "model-check-success": "Model verification succeeded", "model-check-failed": "Model verification failed", "select-provider": "Select Provider", "select-provider-description": "Please select a provider from the left list to configure its settings", "add-provider-form": {"check-key": "Verify", "custom-provider": "Custom Provider", "placeholder": "Please select a provider...", "placeholder-2": "Please enter API Key...", "placeholder-3": "Please enter Base URL...", "provider-select": "Select Provider", "provider-type": "Provider Type", "placeholder-1": "Please enter the provider name...", "provider-name": "Custom Name", "default-name": "Custom Provider", "check-key-success": "API Key Verification Successfully!", "check-key-failed": "API Key Verification failed!", "checking": "Checking...", "unverified": "To Be Verified", "verified": "Verified", "verification-failed": "Failed", "normalized-base-url": "Normalized Base URL", "full-api-endpoint": "Full API Endpoint Example", "apply-normalized-url": "Apply Normalized URL", "url-format-error": "Invalid URL Format", "url-empty-error": "URL cannot be empty", "url-invalid-format": "Invalid URL format", "verification-required": "Please verify the API Key first before adding the provider", "verification-required-notice": "💡 Notice: API verification is required before adding or saving provider configuration.", "verification-hint": "Please click the verify button to confirm API Key availability", "get-api-key": "Click here to get API Key", "api-forward": "API requests will be sent to", "interface-type": "Interface Type", "interface-type-placeholder": "Select interface type", "configure": "Configure", "icon": "Icon", "name": "Name", "name-placeholder": "Please enter provider name"}, "delete": "Delete", "description": " models", "not-configured": "Not configured", "provider-error": "Provider error", "edit": "Edit", "label": "Model Provider", "modal-action": {"add-provider": "Add Provider", "delete": "Delete Provider", "delete-confirm": "Delete", "delete-description": "Please confirm to delete the provider", "delete-description-2": "All models provided by this provider will be deleted,", "delete-description-3": "and assistants that have configured these default models will be changed to other default models.", "edit": "Configure", "add-provider-confirm": "Add"}, "no-provider-description": "No model providers", "edit-provider-form": {"check-key-success": "API Key Verification Successfully!", "check-key-failed": "API Key Verification failed!", "verification-required": "Please verify the API Key first before saving the provider configuration", "verification-required-notice": "💡 Notice: API verification is required before adding or saving provider configuration.", "verification-hint": "Please click the verify button to confirm API Key availability"}, "star": "Collect"}, "name": "Model", "loading": "Loading..."}, "tab-title": "Settings", "tool-settings": {"name": "<PERSON><PERSON> Settings"}, "shortcuts-settings": {"name": "Shortcuts", "title": "Keyboard Shortcuts", "actions": {"send-message": "Send Message", "new-chat": "New Chat", "clear-messages": "Clear Messages", "close-current-tab": "Close Current Tab", "close-other-tabs": "Close Other Tabs", "delete-current-thread": "Delete Current Thread", "open-settings": "Open Settings", "toggle-sidebar": "Toggle Sidebar", "quick-navigation": "Quick Navigation", "command-palette": "Command Palette", "stop-generation": "Stop Generation", "new-tab": "New Tab", "new-session": "New Session", "regenerate-response": "Regenerate Response", "search": "Search", "create-branch": "Create Branch", "close-all-tabs": "Close All Tabs", "restore-last-tab": "Restore Last Tab", "screenshot": "Screenshot", "next-tab": "Next Tab", "previous-tab": "Previous Tab", "toggle-model-panel": "Toggle Model Panel", "toggle-incognito-mode": "Toggle Incognito Mode", "branch-and-send": "Branch and Send", "switch-to-tab-1": "Switch to Tab 1", "switch-to-tab-2": "Switch to Tab 2", "switch-to-tab-3": "Switch to Tab 3", "switch-to-tab-4": "Switch to Tab 4", "switch-to-tab-5": "Switch to Tab 5", "switch-to-tab-6": "Switch to Tab 6", "switch-to-tab-7": "Switch to Tab 7", "switch-to-tab-8": "Switch to Tab 8", "switch-to-tab-9": "Switch to Tab 9", "tab-switching-group": "Quick Tab Switching (1-9)"}, "hints": {"send-message": "Choose how to send messages in chat", "new-chat": "Shortcut to create a new conversation", "clear-messages": "Only clear messages in current session, model settings and other content will be preserved", "close-current-tab": "Close the currently active tab", "close-other-tabs": "Close all tabs except the current one", "delete-current-thread": "Delete the current conversation thread", "open-settings": "Open the settings page", "toggle-sidebar": "Show or hide the sidebar", "quick-navigation": "Quick navigation to different parts of the app", "command-palette": "Open command palette for quick actions", "stop-generation": "Stop the current AI response generation", "new-tab": "Create a new chat tab", "new-session": "Start a new session in current tab", "regenerate-response": "Regenerate the last AI response", "search": "Search within the application", "create-branch": "Create a new branch from current conversation", "close-all-tabs": "Close all open tabs", "restore-last-tab": "Restore the last closed tab", "screenshot": "Take a screenshot (global shortcut)", "next-tab": "Switch to the next tab", "previous-tab": "Switch to the previous tab", "toggle-model-panel": "Show or hide the model selection panel", "toggle-incognito-mode": "Toggle incognito mode for private conversations", "branch-and-send": "Create a branch and send message simultaneously", "switch-to-tab-1": "Switch to tab 1", "switch-to-tab-2": "Switch to tab 2", "switch-to-tab-3": "Switch to tab 3", "switch-to-tab-4": "Switch to tab 4", "switch-to-tab-5": "Switch to tab 5", "switch-to-tab-6": "Switch to tab 6", "switch-to-tab-7": "Switch to tab 7", "switch-to-tab-8": "Switch to tab 8", "switch-to-tab-9": "Switch to tab 9", "tab-switching-group": "Quickly switch to specific tabs"}, "description": {"title": "Keyboard Shortcuts Description", "content": "You can customize keyboard shortcuts for various actions. Only shortcuts from 302.AI Provider models are available for some actions."}, "recorder": {"placeholder": "Click to set shortcut", "press-keys": "Press keys...", "cancel": "Cancel", "reset": "Reset", "clear": "Clear", "no-shortcut": "No shortcut", "error": {"modifier-required": "Shortcuts must include at least one modifier key (Ctrl/Cmd/Alt/Shift)", "shortcut-conflict": "This shortcut already exists, please record again"}}, "scope": {"label": "<PERSON><PERSON>", "app": "App Only", "global": "Global"}}, "text": "Settings", "preference-settings": {"name": "Preferences", "search-provider": {"label": "Default Search Service"}, "display-app-store": {"label": "302.AI App Store", "switch": {"label": "Display App Store on Homepage"}}, "stream-output": {"label": "Stream Output", "smoother": {"label": "<PERSON> S<PERSON>er", "switch": {"label": "Enable smooth streaming output"}}, "speed": {"label": "Output Speed", "slow": "Slow", "normal": "Normal", "fast": "Fast"}}, "collapse-code-block": {"label": "<PERSON><PERSON>", "switch": {"hide-code": "Hide code blocks", "hide-reason": "Hide reason", "collapse-think": "Collapse think blocks", "disable-markdown": "Disable markdown rendering"}}, "model-select": {"label": "New Chat Model", "title-model": "Chat Title Generation Model", "placeholder": "Select Model", "search-placeholder": "Search models...", "no-models-found": "No models found", "use-last-model": "Use Last Chat Model", "use-current-chat-model": "Use the same model as the conversation"}, "title-model-toggle": {"label": "Generate Chat Title", "first-time": "First Conversation", "every-time": "Every Conversation", "off": "Off"}, "parse-url-switch": {"label": "Parse URLs", "switch-desc": "Automatically parse URL content in messages"}}}, "sidebar": {"close-sidebar": {"tooltip": "Close Sidebar"}, "menu-item": {"clean-messages": "Clear Messages", "collect-thread": "Favorite", "generate-title": "Generate Title", "delete": "Delete", "rename": "<PERSON><PERSON>", "uncollect-thread": "Unfavorite", "delete-all": "Delete All"}, "new-thread": {"tooltip": "New Chat"}, "open-sidebar": {"tooltip": "Open Sidebar"}, "search": {"placeholder": "Search..."}, "search-thread": {"tooltip": "Search Conversations", "placeholder": "Search"}, "section": {"collected": "Favorites", "earlier": "Earlier", "last30Days": "Last 30 Days", "last7Days": "Last 7 Days", "today": "Today", "yesterday": "Yesterday", "justNow": "Just Now"}}, "tab-bar": {"menu-item": {"close": "Close", "close-all": "Close All", "reload": "Reload"}}, "thread": {"new-thread-title": "New Chat", "create-thread-error": "Sorry, the new conversation creation failed!", "lack-model": "Please select a model first!", "lack-provider": "Missing model provider!", "selected-model-not-found": "Please select a model first!", "provider-not-found-for-selected-model": "Model provider setup error!", "message-not-found": "This message does not exist!", "failed-to-generate-ai-response": "Failed to generate AI response!", "private-thread-title": "Private Chat"}, "privacy-mode": {"enable-tooltip": "Enable Privacy Mode", "disable-tooltip": "Disable Privacy Mode", "cannot-toggle-tooltip": "Cannot toggle privacy mode after session has started", "cannot-toggle": {"title": "Cannot Toggle Privacy Mode", "description": "Privacy mode can only be changed before the session starts"}, "enabled": {"title": "Privacy Mode Enabled", "description": "This session will not be saved or synced"}, "disabled": {"title": "Privacy Mode Disabled", "description": "This session will be saved normally"}, "error": {"title": "Privacy Mode Error", "description": "Failed to toggle privacy mode"}, "confirm-switch": {"message": "You are about to {{action}}. This will discard your private session content. Continue?"}, "confirm-dialog": {"title": "Confirm Privacy Mode Action", "cancel": "Cancel", "confirm": "Continue"}}, "thread-menu": {"actions": {"cancel": "Cancel", "clean-messages": {"confirmText": "Clear", "description": "Clearing messages will delete all messages and files in the conversation. Are you sure you want to continue?", "title": "Clear Conversation Messages"}, "confirm": "Confirm", "delete": {"confirmText": "Delete", "description": "The conversation cannot be recovered after deletion. Please proceed with caution.", "title": "Are you sure you want to delete this conversation?"}, "rename": {"confirmText": "Confirm", "description": "Please enter a new conversation name.", "edit": {"placeholder": "New conversation name..."}, "title": "Rename Conversation"}, "delete-all": {"title": "Are you sure you want to delete all conversations?", "description": "Except for collected conversations, all other conversations will be deleted and cannot be restored. Please be cautious.", "confirmText": "Delete all"}}}, "common": {"copied-success": "<PERSON><PERSON>d successfully", "copied-failed": "Co<PERSON> failed", "copy-to-clipboard": "Copy"}, "message-list": {"markdown": {"generating-diagram": "Generating a chart...", "waiting-for-diagram": "Waiting for the content of the chart...", "diagram-syntax-error": "Graph syntax error"}}, "message": {"copy": "Copy", "copy-success": "<PERSON><PERSON>d successfully", "copy-failed": "Co<PERSON> failed", "refresh": "Regenerate", "edit": "Edit", "delete": "Delete", "thinking": "AI is thinking", "edit-dialog": {"title": "Edit Message", "save": "Save", "cancel": "Cancel", "save-success": "Message updated successfully", "save-error": "Failed to update message"}, "generate-failed": "Generation failed, please try again...", "delete-success": "Message deleted successfully", "delete-error": "Failed to delete message", "context-menu": {"create-new-branch": "Create new branch from here", "create-new-branch-error": "Sorry, the new branch creation failed!", "new-thread-title": "New Chat", "copy-selected": "<PERSON><PERSON> selected text"}, "think": "Thinking", "outputing": "Outputing...", "reason": "reasoning", "completed": "Completed"}, "stop-generating": "Stop", "new-thread": {"hello-world": "Hi, are you ready to explore the world with me?", "toolbox-label": "AI Applications", "toolbox-button": "More Applications", "toolbox-title": "AI Tools", "toolbox-search-placeholder": "Search"}, "attachment": {"file-too-large": "File size cannot exceed 10MB"}, "artifacts": {"preview": "Preview", "code": "Code", "lines": "lines", "collapse": "Collapse", "expand": "Expand"}, "shortcuts": {"tab-not-exist": "Tab {{tabNumber}} does not exist", "tab-switch-error": "Failed to switch to tab {{tabNumber}}"}, "toolbox": {"tool-press-error-msg": "The API Key of 302.AI provider is not configured or is incorrect, please reconfigure", "tool-collection-error-msg": "Favorite application failed, please try again", "favorites": "Favorites"}, "preview": {"zoom-in": "Zoom In", "zoom-out": "Zoom Out", "rotate-left": "Rotate Left", "rotate-right": "Rotate Right", "reset-zoom": "Reset Zoom", "download": "Download", "copy-code": "Copy Code", "copy-text": "Copy Text", "copy-content": "Copy Content", "toggle-line-numbers": "Toggle Line Numbers", "open-external": "Open with External Viewer", "loading": "Loading...", "loading-code": "Loading code...", "loading-document": "Loading document...", "loading-audio": "Loading audio...", "loading-image": "Loading image...", "loading-text": "Loading text...", "copy-failed": "Co<PERSON> failed", "copied-to-clipboard": "Copied to clipboard", "content-copied": "Content copied", "failed-to-load": "Failed to load", "failed-to-decode": "Failed to decode", "no-content-available": "No content available", "document-parsing-unavailable": "Document parsing unavailable. Please use external viewer.", "file-preview-not-supported": "This file type cannot be previewed in-app.", "close": "Close", "text-preview": "Text Preview", "code-preview": "Code Preview", "text-file": "Text File", "code-file": "Code File", "help-text": {"image": "Use mouse wheel to zoom • Drag to pan • R to rotate • Esc to close", "audio": "Space to play/pause • ← → to skip • M to mute • Esc to close", "code": "Ctrl+C to copy • L to toggle line numbers • + / - to zoom • Esc to close", "document": "+ / - to zoom • Click external link to open in system viewer • Esc to close", "text": "Ctrl+C to copy • + / - to zoom • Esc to close"}}}