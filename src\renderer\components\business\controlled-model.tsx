import { ModalOverlay, type ModalOverlayProps } from "react-aria-components";

interface ControlledModalProps
  extends Pick<ModalOverlayProps, "isOpen" | "onOpenChange" | "children"> {
  children: React.ReactNode;
}

export function ControlledModal({
  isOpen,
  onOpenChange,
  children,
}: ControlledModalProps) {
  return (
    <ModalOverlay
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      isDismissable
      className="fixed inset-0 z-50 min-h-full w-full bg-overlay/50 p-4 text-center backdrop-blur"
    >
      {children}
    </ModalOverlay>
  );
}
