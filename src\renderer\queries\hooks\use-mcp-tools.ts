import type { McpTool } from "@shared/triplit/types";

import { mcpToolsQueries } from "../definitions/mcp-tools-queries";
import type { QueryConfig, QueryOneConfig, QueryResult } from "../types";
import { useStandardQuery, useStandardQueryOne } from "./use-standard-query";

/**
 * 获取所有 MCP tools 记录
 */
export function useMcpTools(
  config?: QueryConfig<"mcpTools">,
): QueryResult<McpTool[]> {
  return useStandardQuery(mcpToolsQueries.all, config);
}

/**
 * 获取启用的 MCP tools
 */
export function useEnabledMcpTools(
  config?: QueryConfig<"mcpTools">,
): QueryResult<McpTool[]> {
  return useStandardQuery(mcpToolsQueries.enabled, config);
}

/**
 * 获取按创建时间排序的 MCP tools
 */
export function useOrderedMcpTools(
  config?: QueryConfig<"mcpTools">,
): QueryResult<McpTool[]> {
  return useStandardQuery(mcpToolsQueries.ordered, config);
}

/**
 * 根据 MCP server ID 获取工具
 */
export function useMcpToolsByServerId(
  serverId: string,
  config?: QueryConfig<"mcpTools">,
): QueryResult<McpTool[]> {
  return useStandardQuery(
    () => {
      if (!serverId) {
        return mcpToolsQueries.byServerId("__nonexistent__");
      }
      return mcpToolsQueries.byServerId(serverId);
    },
    {
      ...config,
      enabled: !!serverId && config?.enabled !== false,
    },
  );
}

/**
 * 根据 MCP server ID 获取启用的工具
 */
export function useEnabledMcpToolsByServerId(
  serverId: string,
  config?: QueryConfig<"mcpTools">,
): QueryResult<McpTool[]> {
  return useStandardQuery(
    () => {
      if (!serverId) {
        return mcpToolsQueries.enabledByServerId("__nonexistent__");
      }
      return mcpToolsQueries.enabledByServerId(serverId);
    },
    {
      ...config,
      enabled: !!serverId && config?.enabled !== false,
    },
  );
}

/**
 * 根据ID获取单个 MCP tool
 */
export function useMcpTool(
  id: string,
  config?: QueryOneConfig<"mcpTools">,
): QueryResult<McpTool | null> {
  return useStandardQueryOne(
    () => {
      if (!id) {
        return mcpToolsQueries.byId("__nonexistent__");
      }
      return mcpToolsQueries.byId(id);
    },
    {
      ...config,
      enabled: !!id && config?.enabled !== false,
    },
  );
}

/**
 * 获取特定服务器的工具数量
 */
export function useMcpToolsCountByServerId(
  serverId: string,
  config?: QueryConfig<"mcpTools">,
) {
  return useStandardQuery(
    () => {
      if (!serverId) {
        return mcpToolsQueries.byServerId("__nonexistent__");
      }
      return mcpToolsQueries.byServerId(serverId);
    },
    {
      ...config,
      enabled: !!serverId && config?.enabled !== false,
      select: (data) => data?.length || 0,
    },
  );
}

/**
 * 获取特定服务器的启用工具数量
 */
export function useEnabledMcpToolsCountByServerId(
  serverId: string,
  config?: QueryConfig<"mcpTools">,
) {
  return useStandardQuery(
    () => {
      if (!serverId) {
        return mcpToolsQueries.enabledByServerId("__nonexistent__");
      }
      return mcpToolsQueries.enabledByServerId(serverId);
    },
    {
      ...config,
      enabled: !!serverId && config?.enabled !== false,
      select: (data) => data?.length || 0,
    },
  );
}