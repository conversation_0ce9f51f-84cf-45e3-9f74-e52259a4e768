import { Label } from "@renderer/components/ui/field";
import { Switch } from "@renderer/components/ui/switch";
import { useEnableUrlParseSetting } from "@renderer/queries";
import { useTranslation } from "react-i18next";

const { settingsService } = window.service;

export function ParseUrlSwitch() {
  const { t } = useTranslation("translation", {
    keyPrefix: "settings.preference-settings.parse-url-switch",
  });

  const { data: enableUrlParse } = useEnableUrlParseSetting();

  const handleChange = async (value: boolean) => {
    await settingsService.setEnableUrlParse(value);
  };

  return (
    <div className="flex flex-col gap-2">
      <Label className="text-label-fg">{t("label")}</Label>
      {enableUrlParse !== undefined && (
        <Switch
          className="h-11 min-w-[398px] rounded-[10px] bg-setting px-3.5 py-2.5"
          isSelected={enableUrlParse ?? false}
          onChange={handleChange}
        >
          <Label className="self-center">{t("switch-desc")}</Label>
        </Switch>
      )}
    </div>
  );
}
