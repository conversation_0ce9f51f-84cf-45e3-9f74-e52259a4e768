import { Schema as S } from "@triplit/client";

export const mcpToolsSchema = {
  schema: S.Schema({
    id: S.Id({ format: "nanoid" }),
    mcpServerId: S.String(),
    name: S.String(),
    description: S.String(),
    parameters: S.<PERSON>tional(S.Record({})),
    enabled: <PERSON><PERSON>({ default: true }),
    createdAt: S.Date({ default: S.Default.now() }),
    updatedAt: S.Date({ default: S.Default.now() }),
  }),
  relationships: {
    mcpServer: S.RelationById("mcpServers", "$mcpServerId"),
  },
};