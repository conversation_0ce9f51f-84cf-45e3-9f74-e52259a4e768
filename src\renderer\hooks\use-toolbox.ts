import { useOrderedToolbox } from "@renderer/queries/hooks/use-toolbox";
import type { Tool } from "@shared/triplit/types";
import { useCallback, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { useTabBar } from "./use-tab-bar";

export interface ToolCategory {
  category: string;
  categoryId: number;
  tools: Tool[];
}

const { toolboxService } = window.service;

export function useToolbox() {
  const { t } = useTranslation("translation", {
    keyPrefix: "toolbox",
  });

  const { data: toolbox, isFetching: toolboxFetching } = useOrderedToolbox();
  const { handleAddNewTab } = useTabBar();

  const [searchQuery, setSearchQuery] = useState("");

  const handleToolPress = useCallback(
    async (tool: Tool) => {
      const result = await toolboxService.getToolUrl(tool.toolId);
      if (!result.isOk) {
        toast.error(t("tool-press-error-msg"));
        return;
      }

      const subdomain = new URL(result.url).hostname.split(".302.ai")[0];
      await handleAddNewTab("302ai-tool", tool.name, {
        subdomain,
      });
    },
    [handleAddNewTab, t],
  );

  const handleToolCollection = useCallback(
    async (toolId: number, collected: boolean) => {
      const result = await toolboxService.updateToolCollection(
        toolId,
        collected,
      );
      if (!result.isOk) {
        toast.error(t("tool-collection-error-msg"));
      }
    },
    [t],
  );

  const categorizedTools = useMemo(() => {
    if (!toolbox) return [];

    const filteredTools = toolbox.filter((tool) => {
      if (!searchQuery.trim()) return true;

      const query = searchQuery.toLowerCase();
      return (
        tool.name.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query) ||
        tool.category.toLowerCase().includes(query)
      );
    });

    // * Create favorites category
    const collectedTools = filteredTools.filter((tool) => tool.collected);
    const favoritesCategory: ToolCategory = {
      category: t("favorites"),
      categoryId: -1,
      tools: collectedTools,
    };

    const groupedByCategory = filteredTools.reduce(
      (acc, tool) => {
        const key = tool.category;
        if (!acc[key]) {
          acc[key] = {
            category: tool.category,
            categoryId: tool.categoryId,
            tools: [],
          };
        }
        acc[key].tools.push(tool);
        return acc;
      },
      {} as Record<string, ToolCategory>,
    );

    const categorizedList = Object.values(groupedByCategory).sort(
      (a, b) => a.categoryId - b.categoryId,
    );

    if (collectedTools.length > 0) {
      return [favoritesCategory, ...categorizedList];
    }

    return categorizedList;
  }, [toolbox, searchQuery, t]);

  const fontDisplayTools = useMemo(() => {
    if (!toolbox || toolbox.length === 0) return [];

    const collectedTools = toolbox.filter((tool) => tool.collected);

    if (collectedTools.length > 0) {
      return collectedTools.slice(0, Math.min(4, collectedTools.length));
    } else {
      // 使用工具ID作为种子来确保排序的稳定性
      const shuffled = [...toolbox].sort((a, b) => {
        // 使用工具ID的哈希值来创建稳定的随机排序
        const hashA = a.toolId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        const hashB = b.toolId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        return (hashA % 1000) - (hashB % 1000);
      });
      return shuffled.slice(0, Math.min(4, shuffled.length));
    }
  }, [toolbox]);

  return {
    fontDisplayTools,
    categorizedTools,
    setSearchQuery,
    searchQuery,
    toolboxFetching,
    handleToolPress,
    handleToolCollection,
  };
}
