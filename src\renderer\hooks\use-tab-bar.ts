import type { DropResult } from "@hello-pangea/dnd";
import { useDefaultPrivacyModeSetting } from "@renderer/queries/hooks/use-settings";
import { useTabs } from "@renderer/queries/hooks/use-tabs";
import logger from "@shared/logger/renderer-logger";
import type { TabType } from "@shared/triplit/types";
import { useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { EventNames, emitter } from "../services/event-service";
import { useActiveTab } from "./use-active-tab";
import { useActiveThread } from "./use-active-thread";
import { usePrivacyMode } from "./use-privacy-mode";

const { tabService } = window.service;

export function useTabBar() {
  const { t } = useTranslation();
  const { activeTabId, activeTab, setActiveTabId } = useActiveTab();
  const { setActiveThreadId } = useActiveThread();
  const { privacyState, inheritPrivacyState } = usePrivacyMode();

  const { data: tabs = [] } = useTabs();

  const { data: defaultPrivacyMode } = useDefaultPrivacyModeSetting();

  const navigate = useNavigate();

  const activateTabId = useCallback(
    async (id: string) => {
      try {
        setActiveTabId(id);
        setTimeout(() => {
          emitter.emit(EventNames.TAB_SELECT, { tabId: id });
        }, 10);
      } catch (error) {
        logger.error("Failed to activate tab", { error });
      }
    },
    [setActiveTabId],
  );

  const handleAddNewTab = useCallback(
    async (type: TabType, title?: string, params?: Record<string, string>) => {
      switch (type) {
        case "thread": {
          const shouldUseDefaultPrivacy =
            privacyState.isPrivate && defaultPrivacyMode;

          if (shouldUseDefaultPrivacy) {
            await inheritPrivacyState();
          } else {
            const newTab = await tabService.insertTab({
              title: t("thread.new-thread-title"),
              type,
              path: "/",
              isPrivate: false,
            });
            const promises = [activateTabId(newTab.id), setActiveThreadId("")];
            await Promise.all(promises);
            logger.info("Tab created", { newTab });
          }

          break;
        }

        case "setting": {
          const existingSettingTab = tabs.find((tab) => tab.type === "setting");
          if (existingSettingTab) {
            const promises = [
              activateTabId(existingSettingTab.id),
              setActiveThreadId(""),
            ];
            await Promise.all(promises);
          } else {
            const newTab = await tabService.insertTab({
              title: t("settings.tab-title"),
              type,
              path: "/settings/general-settings",
              isPrivate: false,
            });
            const promises = [activateTabId(newTab.id), setActiveThreadId("")];
            await Promise.all(promises);
          }
          break;
        }

        case "302ai-tool": {
          const newTab = await tabService.insertTab({
            title: title ?? "",
            type,
            path: `/302ai-tool/${params?.subdomain}`,
            isPrivate: false,
          });
          const promises = [activateTabId(newTab.id), setActiveThreadId("")];
          await Promise.all(promises);
          break;
        }

        default:
          break;
      }

      emitter.emit(EventNames.CODE_PREVIEW_CLOSE, null);
    },
    [
      activateTabId,
      setActiveThreadId,
      t,
      tabs,
      inheritPrivacyState,
      privacyState.isPrivate,
      defaultPrivacyMode,
    ],
  );

  // const handleAddNewTab = async (type: TabType) => {
  //   if (type === "setting") {
  //     const existingSettingTab = tabs.find((tab) => tab.type === "setting");

  //     if (existingSettingTab) {
  //       const promises = [
  //         setActiveTabId(existingSettingTab.id),
  //         setActiveThreadId(""),
  //       ];
  //       await Promise.all(promises);

  //       emitter.emit(EventNames.CODE_PREVIEW_CLOSE, null);

  //       return;
  //     }
  //   }

  //   const shouldUseDefaultPrivacy =
  //     type === "thread" &&
  //     privacyState.isPrivate &&
  //     settings?.defaultPrivacyMode;

  //   if (shouldUseDefaultPrivacy) {
  //     await inheritPrivacyState();
  //   } else {
  //     const newTab = await tabService.insertTab({
  //       title:
  //         type === "thread"
  //           ? t("thread.new-thread-title")
  //           : t("settings.tab-title"),
  //       type,
  //       path: type === "thread" ? "/" : "/settings/general-settings",
  //       isPrivate: false,
  //     });
  //     const promises = [setActiveTabId(newTab.id), setActiveThreadId("")];
  //     await Promise.all(promises);
  //     logger.info("Tab created", { newTab });
  //   }

  //   emitter.emit(EventNames.CODE_PREVIEW_CLOSE, null);
  // };

  const handleDragEnd = async (result: DropResult) => {
    if (!result.destination) {
      return;
    }

    if (result.source.index === result.destination.index) {
      return;
    }

    const fromIndex = result.source.index;
    const toIndex = result.destination.index;

    try {
      await tabService.moveTab(fromIndex, toIndex, tabs);
      logger.debug("Tab order updated successfully");
    } catch (error) {
      logger.error("Failed to move tab:", { error });
    }
  };

  /**
   * * This effect is used to navigate to the home page if the tabs are empty
   */
  useEffect(() => {
    if (tabs.length === 0) {
      navigate("/");
    }
  }, [tabs.length, navigate]);

  /**
   * * This effect is used to navigate to the active tab
   */
  useEffect(() => {
    if (activeTab) {
      navigate(activeTab?.path || "/");
    }
  }, [activeTab, navigate]);

  return {
    tabs,
    activeTabId,
    activateTabId,
    setActiveTabId,
    handleAddNewTab,
    handleDragEnd,
  };
}
