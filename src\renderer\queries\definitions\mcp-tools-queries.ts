import { triplitClient } from "@renderer/client";
import type { FullQueries } from "../types";

/**
 * MCP Tools 集合的标准查询定义
 */
export const mcpToolsQueries: FullQueries<"mcpTools"> & {
  /** 根据 MCP server ID 获取工具 */
  byServerId: (serverId: string) => ReturnType<typeof triplitClient.query<"mcpTools">>;
  /** 根据 MCP server ID 获取启用的工具 */
  enabledByServerId: (serverId: string) => ReturnType<typeof triplitClient.query<"mcpTools">>;
} = {
  /**
   * 获取所有 MCP tools 记录
   */
  all: () => triplitClient.query("mcpTools"),

  /**
   * 获取启用的 MCP tools
   */
  enabled: () => triplitClient.query("mcpTools").Where("enabled", "=", true),

  /**
   * 获取按创建时间排序的 MCP tools
   */
  ordered: () => triplitClient.query("mcpTools").Order("createdAt", "ASC"),

  /**
   * 根据 MCP server ID 获取工具
   */
  byServerId: (serverId: string) =>
    triplitClient.query("mcpTools").Where("mcpServerId", "=", serverId),

  /**
   * 根据 MCP server ID 获取启用的工具
   */
  enabledByServerId: (serverId: string) =>
    triplitClient
      .query("mcpTools")
      .Where("mcpServerId", "=", serverId)
      .Where("enabled", "=", true),

  /**
   * 根据ID获取单个 MCP tool
   */
  byId: (id: string) => triplitClient.query("mcpTools").Id(id),
} as const;