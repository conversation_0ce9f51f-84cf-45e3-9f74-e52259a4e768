# MCP Server 数据存储和查询系统

本实现为 MCP (Model Context Protocol) 服务器提供了完整的数据存储和查询功能。

## 功能特点

### 1. 数据库 Schema
- **MCP Servers 表**: 存储 MCP 服务器配置
- **MCP Tools 表**: 存储与服务器关联的工具信息
- **关系支持**: 一对多关系（服务器-工具）
- **时间戳**: 自动管理创建和更新时间

### 2. 服务器类型支持
- `stdio`: 标准输入/输出连接
- `sse`: Server-Sent Events 连接
- `streamableHTTP`: 流式 HTTP 连接

### 3. 高级设置
- 超时配置
- 自定义请求头
- 自定义环境变量
- 自动工具使用
- 长任务连接保持

## 使用示例

### 在 React 组件中使用查询钩子

```typescript
import { 
  useMcpServers, 
  useEnabledMcpServers,
  useMcpToolsByServerId 
} from '@renderer/queries';

function McpServerList() {
  // 获取所有启用的 MCP 服务器
  const { data: servers, isLoading } = useEnabledMcpServers();
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      {servers?.map(server => (
        <ServerCard key={server.id} server={server} />
      ))}
    </div>
  );
}

function ServerCard({ server }: { server: McpServer }) {
  // 获取该服务器的所有工具
  const { data: tools } = useMcpToolsByServerId(server.id);
  
  return (
    <div>
      <h3>{server.name}</h3>
      <p>{server.description}</p>
      <p>类型: {server.type}</p>
      <p>工具数量: {tools?.length || 0}</p>
    </div>
  );
}
```

### 按类型查询服务器

```typescript
import { useStdioMcpServers, useSseMcpServers } from '@renderer/queries';

function ServersByType() {
  const { data: stdioServers } = useStdioMcpServers();
  const { data: sseServers } = useSseMcpServers();
  
  return (
    <div>
      <h2>Stdio 服务器 ({stdioServers?.length || 0})</h2>
      <h2>SSE 服务器 ({sseServers?.length || 0})</h2>
    </div>
  );
}
```

### 在主进程中使用数据库服务

```typescript
import { McpServerDbService, McpToolDbService } from '@main/services/db-service';

// 创建新的 MCP 服务器
const mcpServerService = new McpServerDbService();

const newServer = await mcpServerService.insertMcpServer({
  name: "Weather API Server",
  description: "提供天气信息的 MCP 服务器",
  type: "stdio",
  command: "python weather-mcp-server.py",
  icon: "🌤️",
  enabled: true,
  advancedSettings: {
    timeout: 30000,
    autoUseTool: true,
    keepLongTaskConnection: false
  }
});

// 为服务器添加工具
const mcpToolService = new McpToolDbService();

await mcpToolService.insertMcpTool({
  mcpServerId: newServer.id,
  name: "get_weather",
  description: "获取指定城市的天气信息",
  parameters: {
    type: "object",
    properties: {
      city: {
        type: "string",
        description: "城市名称"
      }
    },
    required: ["city"]
  }
});
```

## 可用的查询钩子

### MCP Servers
- `useMcpServers()` - 获取所有服务器
- `useEnabledMcpServers()` - 获取启用的服务器
- `useOrderedMcpServers()` - 获取排序的服务器
- `useMcpServersByType(type)` - 按类型获取服务器
- `useMcpServer(id)` - 获取单个服务器
- `useStdioMcpServers()` - 获取 stdio 类型服务器
- `useSseMcpServers()` - 获取 SSE 类型服务器
- `useHttpMcpServers()` - 获取 HTTP 类型服务器

### MCP Tools
- `useMcpTools()` - 获取所有工具
- `useEnabledMcpTools()` - 获取启用的工具
- `useMcpToolsByServerId(serverId)` - 获取特定服务器的工具
- `useEnabledMcpToolsByServerId(serverId)` - 获取特定服务器的启用工具
- `useMcpTool(id)` - 获取单个工具
- `useMcpToolsCountByServerId(serverId)` - 获取工具数量

## 数据库服务方法

### McpServerDbService
- `insertMcpServer(data)` - 创建服务器
- `updateMcpServer(id, data)` - 更新服务器
- `deleteMcpServer(id)` - 删除服务器
- `enableMcpServer(id, enabled)` - 启用/禁用服务器
- `updateMcpServerOrder(id, order)` - 更新排序

### McpToolDbService
- `insertMcpTool(data)` - 创建工具
- `updateMcpTool(id, data)` - 更新工具
- `deleteMcpTool(id)` - 删除工具
- `enableMcpTool(id, enabled)` - 启用/禁用工具
- `clearMcpTools(serverId)` - 清空服务器的所有工具