import { useThreads } from "@renderer/queries/hooks/use-threads";
import { useActiveThreadId } from "@renderer/queries/hooks/use-ui";
import type { Thread } from "@shared/triplit/types";
import { useCallback, useEffect, useState } from "react";

const { uiService } = window.service;

export function useActiveThread() {
  const [selectedThread, setSelectedThreadState] = useState<Thread | null>(
    null,
  );

  // Subscribe to UI state changes
  const { data: activeThreadId } = useActiveThreadId();

  // Subscribe to threads changes
  const { data: threads } = useThreads();

  // Update selectedThread when activeThreadId changes
  useEffect(() => {
    if (!activeThreadId || !threads) {
      setSelectedThreadState(null);
      return;
    }

    const activeThread = threads.find((t) => t.id === activeThreadId);
    setSelectedThreadState(activeThread || null);
  }, [activeThreadId, threads]);

  const setActiveThreadId = useCallback(async (threadId: string) => {
    await uiService.updateActiveThreadId(threadId || "");
  }, []);

  return {
    activeThreadId,
    selectedThread,
    setActiveThreadId,
  };
}
